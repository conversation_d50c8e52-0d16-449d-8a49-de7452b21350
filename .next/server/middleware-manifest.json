{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e76d35f6._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_4c46a49c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JZiPvQ5XzRHs0H9v2y0AQPgZIzl0VWNXe/d02EC1/6o=", "__NEXT_PREVIEW_MODE_ID": "a041644355edc270fd3b12c80ea35dac", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d024d555cedc97037c9f68f78315171e97e5f7e02da569085bb1b2eb471a8392", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e112a59976e4af14baea82b5687a88138f103c4d733d58dedfa41044e494b5c3"}}}, "sortedMiddleware": ["/"], "functions": {}}